# Audio File Management - Frontend

Vue 3 frontend application for the Audio File Management system.

## Features

- Modern Vue 3 with Composition API
- Responsive design with Tailwind CSS
- Audio file browsing and management
- In-browser audio playback
- Search and filtering
- Bulk file operations
- User authentication
- Real-time notifications
- Mobile-friendly interface

## Quick Start

1. **Install dependencies:**
```bash
npm install
```

2. **Start development server:**
```bash
npm run dev
```

3. **Build for production:**
```bash
npm run build
```

4. **Run tests:**
```bash
npm test
```

## Tech Stack

- **Framework**: Vue 3 with Composition API
- **Build Tool**: Vite
- **State Management**: Pinia
- **Routing**: Vue Router
- **Styling**: Tailwind CSS
- **HTTP Client**: Axios
- **UI Components**: Headless UI
- **Icons**: Heroicons
- **Notifications**: Vue Toastification
- **Testing**: Vitest + Vue Test Utils

## Project Structure

```
src/
├── components/
│   ├── AudioFileItem.vue       # File list item component
│   ├── AudioPlayerModal.vue    # Audio player modal
│   └── Pagination.vue          # Pagination component
├── views/
│   ├── LoginView.vue           # Login page
│   ├── DashboardView.vue       # Dashboard page
│   ├── AudioFilesView.vue      # File management page
│   └── NotFoundView.vue        # 404 page
├── stores/
│   ├── auth.js                 # Authentication store
│   └── files.js                # File management store
├── services/
│   └── api.js                  # HTTP client configuration
├── router/
│   └── index.js                # Route definitions
├── App.vue                     # Root component
├── main.js                     # Application entry point
└── style.css                   # Global styles
```

## Environment Variables

Create a `.env.local` file:

```bash
VITE_API_BASE_URL=http://localhost:3000/api
```

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm test` - Run tests
- `npm run test:ui` - Run tests with UI
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier

## Authentication

The application uses JWT tokens stored in localStorage. The auth store handles:

- User login/logout
- Token management
- Route protection
- User session persistence

Default credentials:
- Username: `admin`
- Password: `password`

## State Management

Using Pinia for state management:

### Auth Store (`stores/auth.js`)
- User authentication state
- Login/logout actions
- Token management

### Files Store (`stores/files.js`)
- File listing and pagination
- Search and filtering
- File selection and bulk operations
- Download functionality

## Routing

Protected routes require authentication:

```javascript
{
  path: '/files',
  name: 'AudioFiles',
  component: AudioFilesView,
  meta: { requiresAuth: true }
}
```

## Components

### AudioFileItem
Displays individual file information with actions:
- File selection checkbox
- File metadata (name, size, date)
- Play and download buttons

### AudioPlayerModal
Modal component for audio playback:
- HTML5 audio player
- File information display
- Download functionality
- Error handling

### Pagination
Reusable pagination component:
- Page navigation
- Items per page info
- Mobile-responsive design

## Styling

Using Tailwind CSS with custom components:

```css
.btn {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
  @apply btn bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}
```

## API Integration

HTTP client with automatic error handling:

```javascript
import api from '@/services/api'

// Automatic token injection
// Error handling with toast notifications
// Request/response interceptors
```

## Features

### File Management
- Browse audio files with pagination
- Search by filename
- Sort by name, size, or date
- Select multiple files
- Bulk download operations

### Audio Playback
- In-browser HTML5 audio player
- Streaming from S3 with signed URLs
- Modal interface for focused listening

### Responsive Design
- Mobile-first approach
- Adaptive layouts
- Touch-friendly interactions
- Optimized for various screen sizes

## Development

### Adding New Components

1. Create component in `src/components/`:
```vue
<template>
  <div class="my-component">
    <!-- Template -->
  </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  title: String
})

const emit = defineEmits(['update'])
</script>
```

2. Import and use in parent components

### Adding New Pages

1. Create view in `src/views/`
2. Add route in `src/router/index.js`
3. Add navigation links if needed

### State Management

Create new stores in `src/stores/`:

```javascript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useMyStore = defineStore('myStore', () => {
  const state = ref(initialState)
  
  const getters = computed(() => {
    // Computed values
  })
  
  const actions = {
    // Actions
  }
  
  return { state, getters, ...actions }
})
```

## Testing

Using Vitest for unit testing:

```javascript
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import MyComponent from '@/components/MyComponent.vue'

describe('MyComponent', () => {
  it('renders properly', () => {
    const wrapper = mount(MyComponent, {
      props: { title: 'Test Title' }
    })
    expect(wrapper.text()).toContain('Test Title')
  })
})
```

## Build and Deployment

### Development Build
```bash
npm run dev
```

### Production Build
```bash
npm run build
```

### Docker Deployment
```bash
docker build -t audio-frontend .
docker run -p 80:80 audio-frontend
```

### Static Hosting
The built files in `dist/` can be served by any static hosting service:
- Netlify
- Vercel
- AWS S3 + CloudFront
- GitHub Pages

## Performance Optimization

- Lazy loading for routes
- Component code splitting
- Image optimization
- Bundle size analysis
- Tree shaking

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

Requires HTML5 audio support for playback functionality.

## Troubleshooting

### Common Issues

1. **API Connection**: Check VITE_API_BASE_URL
2. **CORS Errors**: Verify backend CORS configuration
3. **Audio Playback**: Ensure browser supports audio format
4. **Build Errors**: Clear node_modules and reinstall

### Development Tools

- Vue DevTools browser extension
- Vite dev server with HMR
- ESLint for code quality
- Prettier for code formatting
