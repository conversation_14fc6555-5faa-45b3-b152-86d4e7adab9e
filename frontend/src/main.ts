import { createApp } from "vue";
import { createPinia } from "pinia";
import Toast from "vue-toastification";
import "vue-toastification/dist/index.css";
import App from "./App.vue";
import router from "./router";

import "./style.css";
// import vuetify from "./plugins/vuetify";

const app = createApp(App);
const pinia = createPinia();

app.use(pinia);
app.use(Toast);
// app.use(vuetify);
app.use(router);

app.mount("#app");
