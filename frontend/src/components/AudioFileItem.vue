<template>
  <div class="px-6 py-4 hover:bg-gray-50 transition-colors">
    <div class="flex items-center justify-between">
      <div class="flex items-center flex-1 min-w-0">
        <!-- Checkbox -->
        <input
          type="checkbox"
          :checked="selected"
          @change="$emit('select')"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        
        <!-- File Icon -->
        <div class="ml-4 flex-shrink-0">
          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
            </svg>
          </div>
        </div>
        
        <!-- File Info -->
        <div class="ml-4 flex-1 min-w-0">
          <div class="flex items-center justify-between">
            <div class="min-w-0 flex-1">
              <p class="text-sm font-medium text-gray-900 truncate">
                {{ file.name }}
              </p>
              <div class="flex items-center mt-1 text-sm text-gray-500 space-x-4">
                <span>{{ formatFileSize(file.size) }}</span>
                <span>{{ formatDate(file.lastModified) }}</span>
                <span class="uppercase font-medium px-2 py-1 bg-gray-100 rounded text-xs">
                  {{ file.extension }}
                </span>
              </div>
              <!-- Additional metadata if available -->
              <div v-if="file.metadata" class="mt-2 text-xs text-gray-400 space-y-1">
                <div v-if="file.metadata.title && file.metadata.title !== file.name" class="flex items-center">
                  <span class="font-medium">Title:</span>
                  <span class="ml-1 truncate">{{ file.metadata.title }}</span>
                </div>
                <div v-if="file.metadata.description" class="flex items-center">
                  <span class="font-medium">Description:</span>
                  <span class="ml-1 truncate">{{ file.metadata.description }}</span>
                </div>
                <div v-if="file.metadata.tags" class="flex items-center">
                  <span class="font-medium">Tags:</span>
                  <span class="ml-1 truncate">{{ file.metadata.tags }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Actions -->
      <div class="ml-4 flex items-center space-x-2">
        <!-- Play Button -->
        <button
          @click="$emit('play')"
          class="p-2 text-gray-400 hover:text-blue-600 transition-colors"
          title="Play audio"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10v4a2 2 0 002 2h2a2 2 0 002-2v-4M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1"></path>
          </svg>
        </button>
        
        <!-- Download Button -->
        <button
          @click="$emit('download')"
          :disabled="downloading"
          class="p-2 text-gray-400 hover:text-green-600 transition-colors disabled:opacity-50"
          title="Download file"
        >
          <svg v-if="downloading" class="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m-6 4h8"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

defineProps({
  file: {
    type: Object,
    required: true
  },
  selected: {
    type: Boolean,
    default: false
  }
})

defineEmits(['select', 'download', 'play'])

const downloading = ref(false)

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}


</script>
