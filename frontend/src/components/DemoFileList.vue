<template>
  <div class="max-w-7xl mx-auto animate-fadeInUp">
    <div class="card-modern">
      <!-- Demo Banner -->
      <div class="px-6 py-4 border-b border-blue-200/30 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-t-2xl">
        <div class="flex items-center">
          <div class="w-8 h-8 rounded-xl bg-blue-500/20 flex items-center justify-center mr-3">
            <svg class="h-5 w-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-800 mb-1">✨ Preview Mode</h3>
            <p class="text-sm text-gray-600">This is how your audio files will appear once S3 is configured</p>
          </div>
        </div>
      </div>

      <!-- Files List -->
      <div class="divide-y divide-gray-200/30">
        <div
          v-for="file in demoFiles"
          :key="file.id"
          class="px-6 py-6 hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-purple-50/50 transition-all duration-300 group"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center flex-1 min-w-0">
              <input type="checkbox" disabled class="h-5 w-5 text-blue-600 border-gray-300 rounded-lg opacity-50" />
              
              <div class="ml-6 flex-shrink-0">
                <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                </div>
              </div>
            
              <div class="ml-6 flex-1 min-w-0">
                <p class="text-lg font-semibold text-gray-800 truncate mb-2">{{ file.name }}</p>
                <div class="flex items-center text-sm text-gray-500 space-x-4 mb-3">
                  <span class="font-medium">{{ file.size }}</span>
                  <span>{{ file.date }}</span>
                  <span class="uppercase font-bold px-3 py-1 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 rounded-lg text-xs">{{ file.extension }}</span>
                </div>
                <div v-if="file.metadata" class="space-y-2">
                  <div v-if="file.metadata.title" class="flex items-center">
                    <span class="text-xs font-semibold text-gray-600 w-20">Title:</span>
                    <span class="text-sm text-gray-800 font-medium">{{ file.metadata.title }}</span>
                  </div>
                  <div v-if="file.metadata.description" class="flex items-center">
                    <span class="text-xs font-semibold text-gray-600 w-20">Info:</span>
                    <span class="text-sm text-gray-600">{{ file.metadata.description }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="ml-6 flex items-center space-x-3">
              <button disabled class="p-3 text-gray-300 cursor-not-allowed bg-gray-100 rounded-xl" title="Play (demo)">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10v4a2 2 0 002 2h2a2 2 0 002-2v-4M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1"></path>
                </svg>
              </button>
              <button disabled class="p-3 text-gray-300 cursor-not-allowed bg-gray-100 rounded-xl" title="Download (demo)">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m-6 4h8"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const demoFiles = [
  {
    id: 1,
    name: 'summer-vibes-2024.mp3',
    size: '4.2 MB',
    date: 'Dec 15, 2024',
    extension: 'mp3',
    metadata: {
      title: 'Summer Vibes 2024',
      description: 'Upbeat summer track with tropical elements'
    }
  },
  {
    id: 2,
    name: 'podcast-episode-001.wav',
    size: '45.8 MB',
    date: 'Dec 14, 2024',
    extension: 'wav',
    metadata: {
      title: 'Podcast Episode 001',
      description: 'Introduction to our new podcast series'
    }
  },
  {
    id: 3,
    name: 'ambient-forest-sounds.ogg',
    size: '12.1 MB',
    date: 'Dec 13, 2024',
    extension: 'ogg',
    metadata: {
      title: 'Ambient Forest Sounds',
      description: 'Relaxing nature sounds for meditation'
    }
  }
]
</script>
