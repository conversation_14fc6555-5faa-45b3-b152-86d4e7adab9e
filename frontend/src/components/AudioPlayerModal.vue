<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="$emit('close')">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white" @click.stop>
      <!-- Header -->
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Audio Player</h3>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      
      <!-- File Info -->
      <div class="mb-6">
        <div class="flex items-center space-x-4">
          <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
            </svg>
          </div>
          <div>
            <h4 class="text-lg font-medium text-gray-900">{{ file.name }}</h4>
            <div class="flex items-center space-x-4 text-sm text-gray-500">
              <span>{{ formatFileSize(file.size) }}</span>
              <span class="uppercase">{{ file.extension }}</span>
              <span>{{ formatDate(file.lastModified) }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Audio Player -->
      <div class="mb-6">
        <div v-if="loading" class="flex items-center justify-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span class="ml-3 text-gray-600">Loading audio...</span>
        </div>
        
        <div v-else-if="error" class="text-center py-8">
          <div class="text-red-600 mb-2">
            <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <p class="text-red-600 font-medium">Failed to load audio</p>
          <p class="text-sm text-gray-500 mt-1">{{ error }}</p>
          <button
            @click="loadAudio"
            class="mt-4 btn-primary"
          >
            Try Again
          </button>
        </div>
        
        <audio
          v-else
          ref="audioPlayer"
          :src="audioUrl"
          controls
          class="w-full"
          @loadstart="loading = true"
          @canplay="loading = false"
          @error="handleAudioError"
        >
          Your browser does not support the audio element.
        </audio>
      </div>
      
      <!-- Actions -->
      <div class="flex justify-end space-x-3">
        <button
          @click="downloadFile"
          class="btn-outline"
          :disabled="downloading"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m-6 4h8"></path>
          </svg>
          {{ downloading ? 'Downloading...' : 'Download' }}
        </button>
        <button
          @click="$emit('close')"
          class="btn-secondary"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useFilesStore } from '@/stores/files'
import { useToast } from 'vue-toastification'

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const props = defineProps({
  file: {
    type: Object,
    required: true
  }
})

defineEmits(['close'])

const filesStore = useFilesStore()
const toast = useToast()

const audioPlayer = ref(null)
const audioUrl = ref('')
const loading = ref(true)
const error = ref('')
const downloading = ref(false)

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}



const loadAudio = async () => {
  try {
    loading.value = true
    error.value = ''
    audioUrl.value = await filesStore.generateStreamUrl(props.file.key)
  } catch (err) {
    console.error('Error loading audio:', err)
    error.value = err.response?.data?.message || 'Failed to load audio file'
  } finally {
    loading.value = false
  }
}

const handleAudioError = (event) => {
  console.error('Audio playback error:', event)
  error.value = 'Unable to play this audio file'
  loading.value = false
}

const downloadFile = async () => {
  try {
    downloading.value = true
    await filesStore.downloadFile(props.file)
    toast.success(`Downloading ${props.file.name}`)
  } catch (err) {
    console.error('Download error:', err)
    toast.error('Failed to download file')
  } finally {
    downloading.value = false
  }
}

onMounted(() => {
  loadAudio()
})

onUnmounted(() => {
  // Pause audio when component is destroyed
  if (audioPlayer.value) {
    audioPlayer.value.pause()
  }
})
</script>
