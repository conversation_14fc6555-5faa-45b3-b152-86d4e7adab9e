import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/services/api'

export const useFilesStore = defineStore('files', () => {
  // State
  const files = ref([])
  const loading = ref(false)
  const error = ref(null)
  const s3Status = ref({
    connected: false,
    message: '',
    lastChecked: null
  })
  const pagination = ref({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20,
    hasNextPage: false,
    hasPrevPage: false
  })
  const filters = ref({
    search: '',
    sortBy: 'name',
    sortOrder: 'asc'
  })
  const selectedFiles = ref([])

  // Getters
  const hasFiles = computed(() => files.value.length > 0)
  const selectedCount = computed(() => selectedFiles.value.length)
  const isAllSelected = computed(() =>
    files.value.length > 0 && selectedFiles.value.length === files.value.length
  )
  const hasError = computed(() => error.value !== null)
  const isS3Available = computed(() => s3Status.value.connected)

  // Actions
  const checkS3Status = async () => {
    try {
      const response = await api.get('/files/s3-status')

      if (response.data.success) {
        s3Status.value = {
          connected: response.data.s3Status.success,
          message: response.data.s3Status.message,
          lastChecked: new Date().toISOString()
        }
        return s3Status.value
      }
    } catch (error) {
      console.error('Error checking S3 status:', error)
      s3Status.value = {
        connected: false,
        message: 'Failed to check S3 status',
        lastChecked: new Date().toISOString()
      }
      throw error
    }
  }

  const fetchFiles = async (page = 1) => {
    try {
      loading.value = true
      error.value = null

      // Check S3 status first
      await checkS3Status()

      const params = {
        page,
        limit: pagination.value.itemsPerPage,
        search: filters.value.search,
        sortBy: filters.value.sortBy,
        sortOrder: filters.value.sortOrder
      }

      const response = await api.get('/files', { params })

      if (response.data.success) {
        files.value = response.data.data.files
        pagination.value = response.data.data.pagination

        // Clear selection when fetching new data
        selectedFiles.value = []

        return response.data.data
      }
    } catch (err) {
      console.error('Error fetching files:', err)
      error.value = {
        message: err.response?.data?.message || 'Failed to fetch files',
        type: err.response?.status === 503 ? 's3_unavailable' : 'general_error',
        details: err.response?.data?.error || err.message
      }

      // Clear files on error
      files.value = []
      pagination.value = {
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        itemsPerPage: 20,
        hasNextPage: false,
        hasPrevPage: false
      }

      throw err
    } finally {
      loading.value = false
    }
  }

  const searchFiles = async (searchTerm) => {
    filters.value.search = searchTerm
    pagination.value.currentPage = 1
    await fetchFiles(1)
  }

  const sortFiles = async (sortBy, sortOrder = 'asc') => {
    filters.value.sortBy = sortBy
    filters.value.sortOrder = sortOrder
    pagination.value.currentPage = 1
    await fetchFiles(1)
  }

  const generateDownloadUrl = async (fileKey) => {
    try {
      const response = await api.get(`/files/${encodeURIComponent(fileKey)}/download`)
      
      if (response.data.success) {
        return response.data.downloadUrl
      }
    } catch (error) {
      console.error('Error generating download URL:', error)
      throw error
    }
  }

  const generateStreamUrl = async (fileKey) => {
    try {
      const response = await api.get(`/files/${encodeURIComponent(fileKey)}/stream`)
      
      if (response.data.success) {
        return response.data.streamUrl
      }
    } catch (error) {
      console.error('Error generating stream URL:', error)
      throw error
    }
  }

  const generateBulkDownloadUrls = async (fileKeys) => {
    try {
      const response = await api.post('/files/bulk-download', { fileKeys })
      
      if (response.data.success) {
        return response.data.downloadUrls
      }
    } catch (error) {
      console.error('Error generating bulk download URLs:', error)
      throw error
    }
  }

  const downloadFile = async (file) => {
    try {
      const downloadUrl = await generateDownloadUrl(file.key)
      
      // Create temporary link and trigger download
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = file.name
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } catch (error) {
      console.error('Error downloading file:', error)
      throw error
    }
  }

  const downloadSelectedFiles = async () => {
    try {
      const fileKeys = selectedFiles.value.map(file => file.key)
      const downloadUrls = await generateBulkDownloadUrls(fileKeys)
      
      // Download each file
      downloadUrls.forEach((result, index) => {
        if (result.success) {
          const link = document.createElement('a')
          link.href = result.url
          link.download = selectedFiles.value[index].name
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        }
      })
    } catch (error) {
      console.error('Error downloading selected files:', error)
      throw error
    }
  }

  const selectFile = (file) => {
    const index = selectedFiles.value.findIndex(f => f.key === file.key)
    if (index === -1) {
      selectedFiles.value.push(file)
    } else {
      selectedFiles.value.splice(index, 1)
    }
  }

  const selectAllFiles = () => {
    if (isAllSelected.value) {
      selectedFiles.value = []
    } else {
      selectedFiles.value = [...files.value]
    }
  }

  const clearSelection = () => {
    selectedFiles.value = []
  }

  const isFileSelected = (file) => {
    return selectedFiles.value.some(f => f.key === file.key)
  }

  return {
    // State
    files,
    loading,
    error,
    s3Status,
    pagination,
    filters,
    selectedFiles,

    // Getters
    hasFiles,
    selectedCount,
    isAllSelected,
    hasError,
    isS3Available,

    // Actions
    checkS3Status,
    fetchFiles,
    searchFiles,
    sortFiles,
    generateDownloadUrl,
    generateStreamUrl,
    generateBulkDownloadUrls,
    downloadFile,
    downloadSelectedFiles,
    selectFile,
    selectAllFiles,
    clearSelection,
    isFileSelected
  }
})
