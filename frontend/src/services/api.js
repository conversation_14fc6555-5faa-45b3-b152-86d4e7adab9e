import axios from 'axios'

// Debug: Log the environment variable
console.log('🔧 VITE_API_BASE_URL:', import.meta.env.VITE_API_BASE_URL)
console.log('🔧 All env vars:', import.meta.env)

// Create axios instance
const api = axios.create({
  baseURL: '/api', // Use relative URL to leverage Vite proxy
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

console.log('🔧 API baseURL:', api.defaults.baseURL)

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    // Handle different error scenarios
    if (error.response) {
      // Server responded with error status
      const { status } = error.response

      switch (status) {
        case 401:
          // Unauthorized - clear token and redirect to login
          localStorage.removeItem('token')
          delete api.defaults.headers.common['Authorization']

          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
          break
      }
    }

    return Promise.reject(error)
  }
)

export default api
