<template>
  <div class="min-h-screen p-4 sm:p-6 lg:p-8">
    <!-- Modern Header with Glassmorphism -->
    <div class="max-w-7xl mx-auto mb-8 animate-fadeInUp">
      <div class="card-glass p-8 mb-8">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div class="mb-6 lg:mb-0">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 rounded-2xl gradient-primary flex items-center justify-center mr-4 shadow-glow">
                <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
              </div>
              <div>
                <h1 class="text-4xl font-bold text-white mb-2">Audio Files</h1>
                <p class="text-white/80 text-lg">
                  Manage and organize your audio collection
                </p>
              </div>
            </div>

            <!-- Stats Cards -->
            <div class="flex flex-wrap gap-4 mt-6">
              <div class="bg-white/10 backdrop-blur-sm rounded-xl px-4 py-2 border border-white/20">
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-blue-300 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                  </svg>
                  <span class="text-white/90 text-sm font-medium">{{ filesStore.files.length }} Files</span>
                </div>
              </div>
              <div class="bg-white/10 backdrop-blur-sm rounded-xl px-4 py-2 border border-white/20">
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-green-300 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span class="text-white/90 text-sm font-medium">{{ filesStore.selectedCount }} Selected</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex flex-col sm:flex-row gap-4">
            <button
              v-if="filesStore.selectedCount > 0"
              @click="downloadSelected"
              class="btn-glass group"
              :disabled="downloading"
            >
              <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m-6 4h8"></path>
              </svg>
              {{ downloading ? 'Downloading...' : 'Download Selected' }}
            </button>
            <button class="btn-glass group">
              <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
              </svg>
              Upload Files
            </button>
            <button @click="retryLoadFiles" class="btn-glass group">
              <svg class="w-5 h-5 mr-2 group-hover:rotate-180 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Refresh
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Modern Search and Filters -->
    <div class="max-w-7xl mx-auto mb-8 animate-slideInRight">
      <div class="card-glass p-6">
        <div class="flex flex-col lg:flex-row gap-6">
          <!-- Search Bar -->
          <div class="flex-1">
            <div class="relative group">
              <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-white/60 group-focus-within:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </div>
              <input
                v-model="searchQuery"
                @input="handleSearch"
                type="text"
                class="input-modern w-full pl-12 pr-4 text-lg"
                placeholder="Search your audio collection..."
              />
            </div>
          </div>

          <!-- Filter Controls -->
          <div class="flex flex-wrap gap-3">
            <!-- Sort Dropdown -->
            <div class="relative">
              <select
                v-model="sortBy"
                @change="handleSort"
                class="input-modern pr-10 appearance-none cursor-pointer"
              >
                <option value="name">📝 Name</option>
                <option value="size">📊 Size</option>
                <option value="lastModified">📅 Date</option>
              </select>
              <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <svg class="h-4 w-4 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </div>

            <!-- Sort Order Toggle -->
            <button
              @click="toggleSortOrder"
              class="btn-glass group px-4 py-3"
              :title="sortOrder === 'asc' ? 'Sort Ascending' : 'Sort Descending'"
            >
              <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" :class="{ 'rotate-180': sortOrder === 'desc' }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
              </svg>
            </button>

            <!-- View Toggle -->
            <button class="btn-glass group px-4 py-3" title="View Options">
              <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- S3 Status Banner -->
    <div v-if="!filesStore.isS3Available && !filesStore.loading" class="px-4 sm:px-0 mb-6">
      <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-yellow-800">S3 Storage Not Available</h3>
            <div class="mt-2 text-sm text-yellow-700">
              <p>{{ filesStore.s3Status.message || 'AWS S3 storage is not configured. File operations are currently unavailable.' }}</p>
              <p class="mt-1">Please configure AWS credentials to enable file management features.</p>
            </div>
            <div class="mt-4">
              <button
                @click="checkS3Status"
                class="bg-yellow-100 px-3 py-1 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-200 transition-colors"
                :disabled="checkingS3"
              >
                {{ checkingS3 ? 'Checking...' : 'Check S3 Status' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error Display -->
    <div v-if="filesStore.hasError && !filesStore.loading" class="px-4 sm:px-0 mb-6">
      <div class="bg-red-50 border border-red-200 rounded-md p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Error Loading Files</h3>
            <div class="mt-2 text-sm text-red-700">
              <p>{{ filesStore.error.message }}</p>
              <p v-if="filesStore.error.details" class="mt-1 text-xs">{{ filesStore.error.details }}</p>
            </div>
            <div class="mt-4">
              <button
                @click="retryLoadFiles"
                class="bg-red-100 px-3 py-1 rounded-md text-sm font-medium text-red-800 hover:bg-red-200 transition-colors"
                :disabled="filesStore.loading"
              >
                {{ filesStore.loading ? 'Loading...' : 'Retry' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Files List -->
    <div class="px-4 sm:px-0">
      <div v-if="filesStore.loading" class="text-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        <p class="mt-4 text-gray-600">Loading files...</p>
      </div>

      <!-- Show demo when S3 is not available -->
      <div v-else-if="!filesStore.isS3Available && !filesStore.hasError">
        <DemoFileList />
      </div>

      <div v-else-if="!filesStore.hasFiles && !filesStore.hasError" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No audio files found</h3>
        <p class="mt-1 text-sm text-gray-500">Try adjusting your search criteria.</p>
      </div>

      <div v-else class="card">
        <!-- Table Header -->
        <div class="px-6 py-3 border-b border-gray-200 bg-gray-50">
          <div class="flex items-center">
            <input
              type="checkbox"
              :checked="filesStore.isAllSelected"
              @change="filesStore.selectAllFiles"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span class="ml-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
              Select All
            </span>
          </div>
        </div>

        <!-- Files List -->
        <div class="divide-y divide-gray-200">
          <AudioFileItem
            v-for="file in filesStore.files"
            :key="file.key"
            :file="file"
            :selected="filesStore.isFileSelected(file)"
            @select="filesStore.selectFile(file)"
            @download="downloadFile"
            @play="playFile"
          />
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="filesStore.hasFiles" class="mt-6">
        <Pagination
          :current-page="filesStore.pagination.currentPage"
          :total-pages="filesStore.pagination.totalPages"
          :total-items="filesStore.pagination.totalItems"
          :items-per-page="filesStore.pagination.itemsPerPage"
          @page-change="handlePageChange"
        />
      </div>
    </div>

    <!-- Audio Player Modal -->
    <AudioPlayerModal
      v-if="currentAudio"
      :file="currentAudio"
      @close="currentAudio = null"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useFilesStore } from '@/stores/files'
import { useToast } from 'vue-toastification'
import AudioFileItem from '@/components/AudioFileItem.vue'
import Pagination from '@/components/Pagination.vue'
import AudioPlayerModal from '@/components/AudioPlayerModal.vue'
import DemoFileList from '@/components/DemoFileList.vue'

// Simple debounce function
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

const filesStore = useFilesStore()
const toast = useToast()

const searchQuery = ref('')
const sortBy = ref('name')
const sortOrder = ref('asc')
const downloading = ref(false)
const currentAudio = ref(null)
const checkingS3 = ref(false)

const handleSearch = debounce(async () => {
  try {
    await filesStore.searchFiles(searchQuery.value)
  } catch (error) {
    console.error('Search error:', error)
  }
}, 300)

const handleSort = async () => {
  try {
    await filesStore.sortFiles(sortBy.value, sortOrder.value)
  } catch (error) {
    console.error('Sort error:', error)
  }
}

const toggleSortOrder = async () => {
  sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  await handleSort()
}

const handlePageChange = async (page) => {
  try {
    await filesStore.fetchFiles(page)
  } catch (error) {
    console.error('Pagination error:', error)
  }
}

const downloadFile = async (file) => {
  try {
    await filesStore.downloadFile(file)
    toast.success(`Downloading ${file.name}`)
  } catch (error) {
    console.error('Download error:', error)
    toast.error('Failed to download file')
  }
}

const downloadSelected = async () => {
  try {
    downloading.value = true
    await filesStore.downloadSelectedFiles()
    toast.success(`Downloading ${filesStore.selectedCount} files`)
    filesStore.clearSelection()
  } catch (error) {
    console.error('Bulk download error:', error)
    toast.error('Failed to download files')
  } finally {
    downloading.value = false
  }
}

const playFile = (file) => {
  currentAudio.value = file
}

const checkS3Status = async () => {
  try {
    checkingS3.value = true
    await filesStore.checkS3Status()

    if (filesStore.isS3Available) {
      toast.success('S3 connection successful!')
      // Retry loading files if S3 is now available
      await filesStore.fetchFiles()
    } else {
      toast.warning('S3 is still not available')
    }
  } catch (error) {
    console.error('S3 status check error:', error)
    toast.error('Failed to check S3 status')
  } finally {
    checkingS3.value = false
  }
}

const retryLoadFiles = async () => {
  try {
    await filesStore.fetchFiles()
    if (!filesStore.hasError) {
      toast.success('Files loaded successfully!')
    }
  } catch (error) {
    console.error('Retry load files error:', error)
    toast.error('Failed to load files')
  }
}

onMounted(async () => {
  try {
    await filesStore.fetchFiles()
  } catch (error) {
    console.error('Error loading files:', error)
    // Don't show toast error here as the error display will handle it
  }
})
</script>
