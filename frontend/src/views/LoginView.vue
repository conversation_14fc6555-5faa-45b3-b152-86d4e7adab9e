<template>
  <div class="min-h-screen flex">
    <!-- Left Side - Branding & Illustration -->
    <div class="hidden lg:flex lg:w-1/2 relative overflow-hidden">
      <!-- Background Gradient -->
      <div class="absolute inset-0 bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-500"></div>

      <!-- Animated Background Elements -->
      <div class="absolute inset-0">
        <div class="absolute top-20 left-20 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse"></div>
        <div class="absolute bottom-32 right-16 w-24 h-24 bg-white/10 rounded-full blur-lg animate-bounce-slow"></div>
        <div class="absolute top-1/2 left-1/3 w-16 h-16 bg-white/10 rounded-full blur-md animate-pulse"></div>
      </div>

      <!-- Content -->
      <div class="relative z-10 flex flex-col justify-center items-center text-center p-12 text-white">
        <!-- Logo/Brand -->
        <div class="mb-8">
          <div class="w-20 h-20 bg-white/20 rounded-2xl flex items-center justify-center mb-6 backdrop-blur-sm">
            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
            </svg>
          </div>
          <h1 class="text-4xl font-bold font-display mb-4">Audio Vault</h1>
          <p class="text-xl text-white/90 font-light">
            Secure Audio File Management System
          </p>
        </div>

        <!-- Illustration -->
        <div class="relative">
          <div class="w-80 h-64 bg-white/10 rounded-3xl backdrop-blur-sm border border-white/20 p-8">
            <!-- File Icons -->
            <div class="grid grid-cols-3 gap-4 h-full">
              <div class="bg-white/20 rounded-xl flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                </svg>
              </div>
              <div class="bg-white/20 rounded-xl flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12,3V12.26C11.5,12.09 11,12 10.5,12C8,12 6,14 6,16.5C6,19 8,21 10.5,21C13,21 15,19 15,16.5V6H19V3H12Z"/>
                </svg>
              </div>
              <div class="bg-white/20 rounded-xl flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9A3,3 0 0,0 9,12M12,17L18.36,10.64C21.15,7.85 21.15,3.15 18.36,0.36C15.57,-2.43 10.87,-2.43 8.08,0.36C5.29,3.15 5.29,7.85 8.08,10.64L12,17Z"/>
                </svg>
              </div>
              <div class="bg-white/20 rounded-xl flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                </svg>
              </div>
              <div class="bg-white/20 rounded-xl flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M13,9H18.5L13,3.5V9M6,2H14L20,8V20A2,2 0 0,1 18,22H6C4.89,22 4,21.1 4,20V4C4,2.89 4.89,2 6,2M15,18V16H6V18H15M18,14V12H6V14H18Z"/>
                </svg>
              </div>
              <div class="bg-white/20 rounded-xl flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12,2C13.1,2 14,2.9 14,4C14,5.1 13.1,6 12,6C10.9,6 10,5.1 10,4C10,2.9 10.9,2 12,2M21,9V7L15,1H5C3.89,1 3,1.89 3,3V21A2,2 0 0,0 5,23H19A2,2 0 0,0 21,21V9M19,21H5V3H13V9H19V21Z"/>
                </svg>
              </div>
            </div>
          </div>

          <!-- Floating Elements -->
          <div class="absolute -top-4 -right-4 w-12 h-12 bg-white/20 rounded-full backdrop-blur-sm animate-bounce"></div>
          <div class="absolute -bottom-4 -left-4 w-8 h-8 bg-white/20 rounded-full backdrop-blur-sm animate-pulse"></div>
        </div>

        <!-- Features -->
        <div class="mt-12 space-y-4">
          <div class="flex items-center space-x-3">
            <div class="w-2 h-2 bg-white rounded-full"></div>
            <span class="text-white/90">Secure cloud storage</span>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-2 h-2 bg-white rounded-full"></div>
            <span class="text-white/90">Advanced file management</span>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-2 h-2 bg-white rounded-full"></div>
            <span class="text-white/90">Real-time collaboration</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Side - Login Form -->
    <div class="w-full lg:w-1/2 flex items-center justify-center p-8 bg-gray-50">
      <div class="w-full max-w-md">
        <!-- Header -->
        <div class="text-center mb-10">
          <div class="lg:hidden mb-6">
            <div class="w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
              </svg>
            </div>
            <h1 class="text-2xl font-bold font-display text-gray-900">Audio Vault</h1>
          </div>

          <h2 class="text-3xl font-bold font-display text-gray-900 mb-2">Welcome back</h2>
          <p class="text-gray-600">Please sign in to your account</p>
        </div>

        <!-- Login Form -->
        <div class="card-premium">
          <!-- Error Alert -->
          <div v-if="errorMessage" class="bg-red-50 border border-red-200 rounded-xl p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-red-800 font-medium">{{ errorMessage }}</p>
              </div>
              <div class="ml-auto pl-3">
                <button @click="errorMessage = ''" class="text-red-400 hover:text-red-600 transition-colors">
                  <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <form class="space-y-6" @submit.prevent="handleLogin">
            <!-- Username Field -->
            <div class="input-group">
              <div class="relative">
                <div class="input-icon">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                  </svg>
                </div>
                <input
                  id="username"
                  v-model="form.username"
                  name="username"
                  type="text"
                  required
                  :class="[
                    'input-premium input-with-icon',
                    errors.username ? 'border-red-300 text-red-900' : ''
                  ]"
                  placeholder="Enter your username"
                  @blur="validateUsername"
                  @input="clearError('username')"
                />
                <div v-if="errors.username" class="absolute inset-y-0 right-0 pr-6 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
              </div>
              <p v-if="errors.username" class="mt-2 text-sm text-red-600 font-medium">{{ errors.username }}</p>
            </div>

            <!-- Password Field -->
            <div class="input-group">
              <div class="relative">
                <div class="input-icon">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM9 6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9V6z"/>
                  </svg>
                </div>
                <input
                  id="password"
                  v-model="form.password"
                  name="password"
                  :type="showPassword ? 'text' : 'password'"
                  required
                  :class="[
                    'input-premium input-with-icon pr-16',
                    errors.password ? 'border-red-300 text-red-900' : ''
                  ]"
                  placeholder="Enter your password"
                  @blur="validatePassword"
                  @input="clearError('password')"
                />
                <button
                  type="button"
                  @click="showPassword = !showPassword"
                  class="absolute inset-y-0 right-0 pr-6 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg v-if="showPassword" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                  </svg>
                  <svg v-else class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                </button>
              </div>
              <p v-if="errors.password" class="mt-2 text-sm text-red-600 font-medium">{{ errors.password }}</p>
            </div>

            <!-- Remember Me & Forgot Password -->
            <div class="flex items-center justify-between">
              <label class="flex items-center">
                <input
                  id="remember-me"
                  v-model="form.rememberMe"
                  name="remember-me"
                  type="checkbox"
                  class="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500 focus:ring-2"
                />
                <span class="ml-3 text-sm text-gray-600 font-medium">Remember me</span>
              </label>
              <a href="#" class="text-sm text-indigo-600 hover:text-indigo-500 font-medium transition-colors">
                Forgot password?
              </a>
            </div>

            <!-- Login Button -->
            <button
              type="submit"
              :disabled="loading || !isFormValid"
              class="btn-premium w-full"
            >
              <svg v-if="loading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ loading ? 'Signing in...' : 'Sign In' }}
            </button>
          </form>

          <!-- Divider -->
          <div class="mt-8 mb-6">
            <div class="relative">
              <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-gray-200"></div>
              </div>
              <div class="relative flex justify-center text-sm">
                <span class="px-4 bg-white text-gray-500 font-medium">Or continue with</span>
              </div>
            </div>
          </div>

          <!-- Social Login -->
          <div class="grid grid-cols-2 gap-3 mb-8">
            <button class="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200 hover:shadow-md">
              <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              Google
            </button>
            <button class="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200 hover:shadow-md">
              <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.024-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.097.118.112.221.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
              </svg>
              Microsoft
            </button>
          </div>

          <!-- Demo Credentials -->
          <div class="mt-8 pt-6 border-t border-gray-200">
            <div class="text-center">
              <p class="text-sm text-gray-600 mb-4 font-medium">Demo Credentials:</p>
              <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 border border-gray-200">
                <div class="flex justify-between items-center text-sm font-mono mb-2">
                  <span class="text-gray-700 font-medium">Username:</span>
                  <span class="font-bold text-gray-900 bg-white px-2 py-1 rounded-lg">admin</span>
                </div>
                <div class="flex justify-between items-center text-sm font-mono">
                  <span class="text-gray-700 font-medium">Password:</span>
                  <span class="font-bold text-gray-900 bg-white px-2 py-1 rounded-lg">password</span>
                </div>
              </div>
              <div class="flex gap-3 mt-4 justify-center">
                <button
                  @click="fillDemoCredentials"
                  class="px-4 py-2 text-sm font-medium text-indigo-600 bg-indigo-50 hover:bg-indigo-100 rounded-lg transition-colors duration-200"
                >
                  Use demo credentials
                </button>
                <button
                  @click="testConnection"
                  class="px-4 py-2 text-sm font-medium text-green-600 bg-green-50 hover:bg-green-100 rounded-lg transition-colors duration-200"
                  :disabled="testingConnection"
                >
                  {{ testingConnection ? 'Testing...' : 'Test Connection' }}
                </button>
              </div>
              <div v-if="connectionStatus" class="mt-3 text-sm font-medium p-3 rounded-lg" :class="connectionStatus.success ? 'text-green-700 bg-green-50' : 'text-red-700 bg-red-50'">
                {{ connectionStatus.message }}
              </div>
            </div>
          </div>

          <!-- Sign Up Link -->
          <div class="mt-8 text-center">
            <p class="text-sm text-gray-600">
              Don't have an account?
              <router-link to="/register" class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors">
                Create account
              </router-link>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'

const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()

// Reactive state
const loading = ref(false)
const showPassword = ref(false)
const errorMessage = ref('')
const testingConnection = ref(false)
const connectionStatus = ref(null)

const form = reactive({
  username: '',
  password: '',
  rememberMe: false
})

const errors = reactive({
  username: '',
  password: ''
})

// Computed properties
const isFormValid = computed(() => {
  return form.username.trim() !== '' &&
         form.password.trim() !== '' &&
         !errors.username &&
         !errors.password
})

// Validation functions
const validateUsername = () => {
  if (!form.username.trim()) {
    errors.username = 'Username is required'
    return false
  }
  if (form.username.length < 3) {
    errors.username = 'Username must be at least 3 characters'
    return false
  }
  errors.username = ''
  return true
}

const validatePassword = () => {
  if (!form.password.trim()) {
    errors.password = 'Password is required'
    return false
  }
  if (form.password.length < 6) {
    errors.password = 'Password must be at least 6 characters'
    return false
  }
  errors.password = ''
  return true
}

const clearError = (field) => {
  errors[field] = ''
  errorMessage.value = ''
}

const validateForm = () => {
  const isUsernameValid = validateUsername()
  const isPasswordValid = validatePassword()
  return isUsernameValid && isPasswordValid
}

// Demo credentials helper
const fillDemoCredentials = () => {
  form.username = 'admin'
  form.password = 'password'
  errors.username = ''
  errors.password = ''
  errorMessage.value = ''
}

// Test connection helper
const testConnection = async () => {
  testingConnection.value = true
  connectionStatus.value = null

  const apiUrl = '/api' // Use relative URL to leverage Vite proxy
  const healthUrl = `${apiUrl}/health`

  console.log('🔧 Environment variable:', import.meta.env.VITE_API_BASE_URL)
  console.log('🔧 Trying to connect to:', healthUrl)

  try {
    const response = await fetch(healthUrl)
    if (response.ok) {
      const data = await response.json()
      connectionStatus.value = {
        success: true,
        message: `✅ Connected to ${healthUrl}! Server: ${data.status}`
      }
    } else {
      connectionStatus.value = {
        success: false,
        message: `❌ Server error ${response.status} from ${healthUrl}`
      }
    }
  } catch (error) {
    connectionStatus.value = {
      success: false,
      message: `❌ Failed to reach ${healthUrl}: ${error.message}`
    }
  } finally {
    testingConnection.value = false
  }
}

// Login handler with enhanced error handling
const handleLogin = async () => {
  // Clear previous errors
  errorMessage.value = ''

  // Validate form
  if (!validateForm()) {
    errorMessage.value = 'Please fix the errors above'
    return
  }

  try {
    loading.value = true

    // Attempt login
    await authStore.login({
      username: form.username.trim(),
      password: form.password
    })

    // Success feedback
    toast.success('Welcome back! Login successful.')

    // Store remember me preference
    if (form.rememberMe) {
      localStorage.setItem('rememberMe', 'true')
      localStorage.setItem('lastUsername', form.username.trim())
    } else {
      localStorage.removeItem('rememberMe')
      localStorage.removeItem('lastUsername')
    }

    // Redirect to dashboard
    router.push('/dashboard')

  } catch (error) {
    console.error('Login error:', error)

    // Handle different error types
    if (error.response) {
      const status = error.response.status
      const message = error.response.data?.message || 'Login failed'

      switch (status) {
        case 401:
          errorMessage.value = 'Invalid username or password. Please try again.'
          break
        case 429:
          errorMessage.value = 'Too many login attempts. Please try again later.'
          break
        case 500:
          errorMessage.value = 'Server error. Please try again later.'
          break
        default:
          errorMessage.value = message
      }
    } else if (error.request) {
      errorMessage.value = 'Unable to connect to server. Please check your internet connection.'
    } else {
      errorMessage.value = 'An unexpected error occurred. Please try again.'
    }

    // Also show toast for immediate feedback
    toast.error(errorMessage.value)

  } finally {
    loading.value = false
  }
}

// Load remembered credentials on mount
onMounted(() => {
  const rememberMe = localStorage.getItem('rememberMe')
  const lastUsername = localStorage.getItem('lastUsername')

  if (rememberMe === 'true' && lastUsername) {
    form.username = lastUsername
    form.rememberMe = true
  }
})
</script>
