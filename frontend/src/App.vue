<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- Premium Navigation Header -->
    <nav v-if="authStore.isAuthenticated" class="bg-white/95 backdrop-blur-lg border-b border-gray-200/50 sticky top-0 z-50 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo & Brand -->
          <div class="flex items-center">
            <router-link to="/dashboard" class="flex items-center group">
              <div class="w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center mr-3 group-hover:scale-105 transition-transform duration-200 shadow-lg">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                </svg>
              </div>
              <div>
                <h1 class="text-lg font-bold font-display text-gray-900 group-hover:text-indigo-600 transition-colors">
                  Audio Vault
                </h1>
                <p class="text-xs text-gray-500 font-medium">File Management</p>
              </div>
            </router-link>
          </div>

          <!-- Navigation Links -->
          <div class="hidden md:flex items-center space-x-8">
            <router-link
              to="/dashboard"
              class="text-sm font-medium text-gray-700 hover:text-indigo-600 transition-colors px-3 py-2 rounded-lg hover:bg-indigo-50"
              :class="{ 'text-indigo-600 bg-indigo-50': $route.path === '/dashboard' }"
            >
              Dashboard
            </router-link>
            <router-link
              to="/files"
              class="text-sm font-medium text-gray-700 hover:text-indigo-600 transition-colors px-3 py-2 rounded-lg hover:bg-indigo-50"
              :class="{ 'text-indigo-600 bg-indigo-50': $route.path === '/files' }"
            >
              Files
            </router-link>
          </div>

          <!-- User Menu -->
          <div class="flex items-center space-x-4">
            <!-- Notifications -->
            <button class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v2.25H2.25V12l2.25-2.25V9.75a6 6 0 0 1 6-6z"></path>
              </svg>
            </button>

            <!-- User Profile -->
            <div class="flex items-center space-x-3 bg-gray-50 rounded-xl px-3 py-2">
              <div class="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span class="text-sm font-bold text-white">
                  {{ authStore.user?.username?.charAt(0).toUpperCase() || 'A' }}
                </span>
              </div>
              <div class="hidden sm:block">
                <p class="text-sm font-semibold text-gray-900">{{ authStore.user?.username || 'Admin' }}</p>
                <p class="text-xs text-gray-500">{{ authStore.user?.role || 'Administrator' }}</p>
              </div>
            </div>

            <!-- Logout Button -->
            <button
              @click="handleLogout"
              class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
              </svg>
              <span class="hidden sm:inline">Logout</span>
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="flex-1 relative">
      <router-view />
    </main>

    <!-- Modern Loading Overlay -->
    <div
      v-if="loading"
      class="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50"
    >
      <div class="card-glass p-8 flex flex-col items-center space-y-4 animate-fadeInUp">
        <!-- Modern Spinner -->
        <div class="relative">
          <div class="w-12 h-12 rounded-full border-4 border-white/20"></div>
          <div class="absolute top-0 left-0 w-12 h-12 rounded-full border-4 border-transparent border-t-white animate-spin"></div>
        </div>
        <div class="text-center">
          <p class="text-white font-medium">Loading...</p>
          <p class="text-white/70 text-sm">Please wait a moment</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'

const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()
const loading = ref(false)

const handleLogout = async () => {
  try {
    loading.value = true
    await authStore.logout()
    router.push('/login')
    toast.success('Logged out successfully')
  } catch (error) {
    toast.error('Error logging out')
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  // Check if user is already authenticated
  if (authStore.token) {
    try {
      await authStore.fetchUser()
    } catch (error) {
      console.error('Error fetching user:', error)
      authStore.logout()
    }
  }
})
</script>
