import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite'
import { fileURLToPath, URL } from 'node:url'
import AutoImport from 'unplugin-auto-import/vite'
// https://vite.dev/config/
export default defineConfig({
  plugins: [vue(),  tailwindcss(),
    AutoImport({
    imports: [
      'vue',
      {
        'vue-router/auto': ['useRoute', 'useRouter'],
      }
    ],
    dts: 'src/auto-imports.d.ts',
    eslintrc: {
      enabled: true,
    },
    vueTemplate: true,
  })
],
  build: {
    emptyOutDir: true,
    outDir: '../public/dist'
  },
  base: '/audio-manager',
  define: { 'process.env': {} },
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false,
      }
    }
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
    extensions: [
      '.js',
      '.json',
      '.jsx',
      '.mjs',
      '.ts',
      '.tsx',
      '.vue',
    ],
  },

})





