<!DOCTYPE html>
<html>
<head>
    <title>Connection Test</title>
</head>
<body>
    <h1>Backend Connection Test</h1>
    <button onclick="testConnection()">Test Connection</button>
    <div id="result"></div>

    <script>
        async function testConnection() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                console.log('Testing connection to: http://localhost:3001/api/health');
                const response = await fetch('http://localhost:3001/api/health');
                console.log('Response:', response);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('Data:', data);
                    resultDiv.innerHTML = `✅ Success! ${JSON.stringify(data)}`;
                } else {
                    resultDiv.innerHTML = `❌ Error: ${response.status} ${response.statusText}`;
                }
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `❌ Failed: ${error.message}`;
            }
        }
    </script>
</body>
</html>
