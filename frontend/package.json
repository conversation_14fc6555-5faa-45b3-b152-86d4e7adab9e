{"name": "audio-file-management-frontend", "version": "1.0.0", "description": "Frontend for Audio File Management application", "type": "module", "scripts": {"build": "cross-env NODE_ENV=prod VITE_APP_STAGE=prod vite build", "build:dev": "cross-env NODE_ENV=develop VITE_APP_STAGE=develop vite build", "dev": "cross-env BROWSER=none VITE_APP_STAGE=develop NODE_ENV=develop vite", "preview": "vite preview"}, "dependencies": {"@vueuse/core": "^13.1.0", "lucide-vue-next": "^0.503.0", "papaparse": "^5.5.2", "pinia": "^3.0.2", "reka-ui": "^2.2.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue-toastification": "^2.0.0-rc.5"}, "gitHooks": {"pre-commit": "lint-staged"}, "devDependencies": {"@tailwindcss/vite": "^4.1.11", "@tsconfig/node22": "^22.0.0", "@types/google.maps": "^3.58.1", "@types/node": "^22.14.1", "@vitejs/plugin-vue": "^5.2.2", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.3.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint": "^9.18.0", "eslint-plugin-vue": "^9.32.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "postcss": "^8.5.6", "prettier": "^3.0.0", "sass": "^1.83.4", "tailwindcss": "^4.1.11", "typescript": "~5.7.3", "unplugin-auto-import": "^0.17.6", "unplugin-fonts": "^1.1.1", "unplugin-vue-components": "^28.0.0", "unplugin-vue-router": "^0.10.0", "vite": "^6.3.1", "vite-plugin-vue-devtools": "^7.7.0", "vite-plugin-vue-layouts": "^0.11.0", "vite-plugin-vuetify": "^2.0.3", "vue-tsc": "^2.2.0"}}