# Development Guide

## Getting Started

### Prerequisites

- Node.js 18 or higher
- npm or yarn
- AWS S3 bucket access (for testing)
- Git

### Quick Setup

1. **Clone and setup:**
```bash
git clone <repository-url>
cd Audio_File_Management
chmod +x scripts/setup.sh
./scripts/setup.sh
```

2. **Configure environment:**
```bash
cp .env.example .env
# Edit .env with your AWS credentials
```

3. **Start development servers:**
```bash
chmod +x scripts/dev.sh
./scripts/dev.sh
```

Or manually:
```bash
# Terminal 1 - Backend
cd backend
npm run dev

# Terminal 2 - Frontend
cd frontend
npm run dev
```

## Project Structure

```
Audio_File_Management/
├── backend/                 # Node.js/Express API
│   ├── src/
│   │   ├── config/         # Configuration files
│   │   ├── middleware/     # Express middleware
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   └── server.js       # Entry point
│   ├── package.json
│   └── Dockerfile
├── frontend/               # Vue 3 application
│   ├── src/
│   │   ├── components/     # Vue components
│   │   ├── views/          # Page components
│   │   ├── stores/         # Pinia stores
│   │   ├── services/       # API services
│   │   ├── router/         # Vue Router
│   │   └── main.js         # Entry point
│   ├── package.json
│   └── Dockerfile
├── docs/                   # Documentation
├── scripts/                # Build/deployment scripts
├── docker-compose.yml      # Docker configuration
└── README.md
```

## Backend Development

### Architecture

- **Framework**: Express.js
- **Authentication**: JWT tokens
- **Storage**: AWS S3
- **Validation**: express-validator
- **Security**: helmet, cors, rate limiting

### Key Files

- `src/server.js` - Main application entry point
- `src/config/aws.js` - AWS S3 configuration
- `src/middleware/auth.js` - Authentication middleware
- `src/routes/` - API route definitions
- `src/services/fileService.js` - S3 file operations

### Adding New Routes

1. Create route file in `src/routes/`:
```javascript
const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const router = express.Router();

router.get('/', authenticateToken, (req, res) => {
  res.json({ message: 'Hello World' });
});

module.exports = router;
```

2. Register in `src/server.js`:
```javascript
app.use('/api/newroute', require('./routes/newroute'));
```

### Testing

```bash
cd backend
npm test
npm run test:watch
```

## Frontend Development

### Architecture

- **Framework**: Vue 3 with Composition API
- **State Management**: Pinia
- **Routing**: Vue Router
- **Styling**: Tailwind CSS
- **HTTP Client**: Axios
- **Build Tool**: Vite

### Key Files

- `src/main.js` - Application entry point
- `src/App.vue` - Root component
- `src/router/index.js` - Route definitions
- `src/stores/` - Pinia stores for state management
- `src/services/api.js` - HTTP client configuration

### Adding New Pages

1. Create view component in `src/views/`:
```vue
<template>
  <div>
    <h1>New Page</h1>
  </div>
</template>

<script setup>
// Component logic here
</script>
```

2. Add route in `src/router/index.js`:
```javascript
{
  path: '/newpage',
  name: 'NewPage',
  component: () => import('@/views/NewPageView.vue'),
  meta: { requiresAuth: true }
}
```

### State Management

Using Pinia for state management:

```javascript
// stores/example.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useExampleStore = defineStore('example', () => {
  const items = ref([])
  const loading = ref(false)
  
  const itemCount = computed(() => items.value.length)
  
  const fetchItems = async () => {
    loading.value = true
    // API call logic
    loading.value = false
  }
  
  return { items, loading, itemCount, fetchItems }
})
```

### Testing

```bash
cd frontend
npm test
npm run test:ui
```

## Code Style and Standards

### Backend (JavaScript)

- Use ESLint with Standard config
- Prefer async/await over promises
- Use meaningful variable names
- Add JSDoc comments for functions
- Handle errors properly

```javascript
/**
 * Generate signed URL for file download
 * @param {string} fileKey - S3 object key
 * @returns {Promise<string>} Signed URL
 */
async function generateDownloadUrl(fileKey) {
  try {
    // Implementation
  } catch (error) {
    console.error('Error generating download URL:', error);
    throw error;
  }
}
```

### Frontend (Vue 3)

- Use Composition API
- Prefer `<script setup>` syntax
- Use TypeScript-style prop definitions
- Follow Vue 3 style guide
- Use Tailwind CSS classes

```vue
<template>
  <div class="container mx-auto px-4">
    <h1 class="text-2xl font-bold">{{ title }}</h1>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  title: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update'])
</script>
```

## API Integration

### Making API Calls

Use the configured axios instance:

```javascript
import api from '@/services/api'

// In a store or component
const fetchData = async () => {
  try {
    const response = await api.get('/files')
    return response.data
  } catch (error) {
    console.error('API error:', error)
    throw error
  }
}
```

### Error Handling

The API service automatically handles common errors and shows toast notifications. For custom error handling:

```javascript
try {
  await api.post('/files/upload', formData)
  toast.success('File uploaded successfully')
} catch (error) {
  if (error.response?.status === 413) {
    toast.error('File too large')
  }
  // Other errors are handled automatically
}
```

## Environment Variables

### Backend (.env)
```bash
NODE_ENV=development
PORT=3000
JWT_SECRET=your-secret-key
AWS_ACCESS_KEY_ID=your-key
AWS_SECRET_ACCESS_KEY=your-secret
S3_BUCKET_NAME=your-bucket
```

### Frontend (.env.local)
```bash
VITE_API_BASE_URL=http://localhost:3000/api
```

## Debugging

### Backend Debugging

1. **Using Node.js debugger:**
```bash
node --inspect src/server.js
```

2. **Using VS Code:**
Add to `.vscode/launch.json`:
```json
{
  "type": "node",
  "request": "launch",
  "name": "Debug Backend",
  "program": "${workspaceFolder}/backend/src/server.js",
  "env": {
    "NODE_ENV": "development"
  }
}
```

### Frontend Debugging

1. **Vue DevTools**: Install browser extension
2. **Console logging**: Use `console.log` for debugging
3. **Vite debugging**: Built-in source maps

## Common Development Tasks

### Adding Authentication to Routes

Backend:
```javascript
router.get('/protected', authenticateToken, (req, res) => {
  // Route logic
});
```

Frontend:
```javascript
// In router/index.js
{
  path: '/protected',
  component: ProtectedView,
  meta: { requiresAuth: true }
}
```

### File Upload Handling

Backend:
```javascript
const multer = require('multer');
const upload = multer({ dest: 'uploads/' });

router.post('/upload', authenticateToken, upload.single('file'), (req, res) => {
  // Handle file upload
});
```

Frontend:
```javascript
const uploadFile = async (file) => {
  const formData = new FormData();
  formData.append('file', file);
  
  await api.post('/files/upload', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  });
};
```

## Performance Optimization

### Backend
- Use compression middleware
- Implement caching for S3 operations
- Use connection pooling for databases
- Optimize S3 list operations

### Frontend
- Lazy load routes and components
- Implement virtual scrolling for large lists
- Use image optimization
- Minimize bundle size

## Security Best Practices

1. **Input Validation**: Always validate user input
2. **Authentication**: Use JWT tokens with expiration
3. **Authorization**: Check user permissions
4. **CORS**: Configure proper origins
5. **Rate Limiting**: Prevent abuse
6. **HTTPS**: Use in production
7. **Environment Variables**: Never commit secrets
