# Deployment Guide

## Prerequisites

- Node.js 18 or higher
- AWS S3 bucket with appropriate permissions
- Domain name (for production)
- SSL certificate (for production)

## Environment Configuration

### Required Environment Variables

Create a `.env` file in the project root with the following variables:

```bash
# Backend Configuration
NODE_ENV=production
PORT=3000
API_BASE_URL=https://your-domain.com

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random
JWT_EXPIRES_IN=24h

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-audio-files-bucket

# CORS Configuration
CORS_ORIGIN=https://your-frontend-domain.com

# File Configuration
MAX_FILE_SIZE=100MB
ALLOWED_AUDIO_FORMATS=mp3,wav,ogg,m4a,aac
```

## AWS S3 Setup

### 1. Create S3 Bucket

```bash
aws s3 mb s3://your-audio-files-bucket --region us-east-1
```

### 2. Configure Bucket Policy

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "AllowAudioFileAccess",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::YOUR-ACCOUNT-ID:user/YOUR-IAM-USER"
      },
      "Action": [
        "s3:GetObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::your-audio-files-bucket",
        "arn:aws:s3:::your-audio-files-bucket/*"
      ]
    }
  ]
}
```

### 3. Configure CORS

```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["GET", "HEAD"],
    "AllowedOrigins": ["https://your-frontend-domain.com"],
    "ExposeHeaders": ["ETag"],
    "MaxAgeSeconds": 3000
  }
]
```

## Deployment Options

### Option 1: Docker Deployment

#### Development
```bash
docker-compose up -d
```

#### Production
```bash
docker-compose --profile production up -d
```

### Option 2: Manual Deployment

#### Backend Deployment

1. **Install dependencies:**
```bash
cd backend
npm ci --production
```

2. **Start with PM2:**
```bash
npm install -g pm2
pm2 start src/server.js --name "audio-backend"
pm2 startup
pm2 save
```

#### Frontend Deployment

1. **Build the application:**
```bash
cd frontend
npm ci
npm run build
```

2. **Serve with Nginx:**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/frontend/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### Option 3: Cloud Deployment

#### AWS Elastic Beanstalk

1. **Install EB CLI:**
```bash
pip install awsebcli
```

2. **Initialize and deploy:**
```bash
eb init
eb create production
eb deploy
```

#### Heroku

1. **Create Heroku apps:**
```bash
heroku create your-app-backend
heroku create your-app-frontend
```

2. **Set environment variables:**
```bash
heroku config:set NODE_ENV=production -a your-app-backend
heroku config:set JWT_SECRET=your-secret -a your-app-backend
# ... set other variables
```

3. **Deploy:**
```bash
git subtree push --prefix backend heroku-backend main
git subtree push --prefix frontend heroku-frontend main
```

## SSL Configuration

### Let's Encrypt with Certbot

```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### Manual SSL Certificate

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # ... rest of configuration
}
```

## Monitoring and Logging

### PM2 Monitoring

```bash
pm2 monit
pm2 logs
pm2 restart all
```

### Log Rotation

```bash
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
```

## Security Considerations

1. **Use strong JWT secrets**
2. **Enable HTTPS in production**
3. **Configure proper CORS origins**
4. **Use environment variables for sensitive data**
5. **Regularly update dependencies**
6. **Implement rate limiting**
7. **Use secure headers**

## Backup Strategy

### Database Backup (if using database)
```bash
# PostgreSQL
pg_dump -h localhost -U username dbname > backup.sql

# MongoDB
mongodump --host localhost --db dbname --out backup/
```

### S3 Backup
Enable S3 versioning and cross-region replication for audio files.

## Troubleshooting

### Common Issues

1. **CORS errors**: Check CORS_ORIGIN environment variable
2. **S3 access denied**: Verify AWS credentials and bucket permissions
3. **JWT errors**: Ensure JWT_SECRET is set and consistent
4. **File upload issues**: Check file size limits and formats

### Health Checks

- Backend: `GET /api/health`
- Frontend: Check if main page loads
- S3 connectivity: Test file listing endpoint
