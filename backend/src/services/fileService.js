const { s3, S3_CONFIG, testS3Connection } = require('../config/aws');
const { generateS3Key } = require('../middleware/s3Auth');
const path = require('path');

class FileService {
  /**
   * Initialize service and verify S3 connection
   */
  async initialize() {
    const connectionTest = await testS3Connection();
    if (!connectionTest.success) {
      console.warn('⚠️  FileService initialized without S3 connection');
    }
    return connectionTest;
  }

  /**
   * Upload file to S3
   */
  async uploadFile(file, userId, metadata = {}) {
    try {
      // Verify S3 connection first
      const connectionTest = await testS3Connection();
      if (!connectionTest.success) {
        throw new Error('S3 service unavailable');
      }

      const s3Key = generateS3Key(userId, file.originalname);

      const uploadParams = {
        Bucket: S3_CONFIG.bucketName,
        Key: s3Key,
        Body: file.buffer,
        ContentType: file.mimetype,
        Metadata: {
          originalName: file.originalname,
          uploadedBy: userId.toString(),
          uploadDate: new Date().toISOString(),
          ...metadata
        },
        ServerSideEncryption: 'AES256'
      };

      const result = await s3.upload(uploadParams).promise();

      return {
        key: result.Key,
        location: result.Location,
        bucket: result.Bucket,
        size: file.size,
        originalName: file.originalname,
        contentType: file.mimetype,
        uploadedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  }
  /**
   * List audio files from S3 bucket
   */
  async listFiles({ page = 1, limit = 20, search = '', sortBy = 'name', sortOrder = 'asc' }) {
    try {
      const params = {
        Bucket: S3_CONFIG.bucketName,
        MaxKeys: 1000 // Get more items to handle filtering and pagination
      };

      const data = await s3.listObjectsV2(params).promise();
      
      // Filter audio files only
      let audioFiles = data.Contents.filter(file => {
        const ext = path.extname(file.Key).toLowerCase().substring(1);
        return S3_CONFIG.allowedAudioFormats.includes(ext);
      });

      // Apply search filter
      if (search) {
        audioFiles = audioFiles.filter(file => 
          file.Key.toLowerCase().includes(search.toLowerCase())
        );
      }

      // Sort files
      audioFiles.sort((a, b) => {
        let aValue, bValue;
        
        switch (sortBy) {
          case 'size':
            aValue = a.Size;
            bValue = b.Size;
            break;
          case 'lastModified':
            aValue = new Date(a.LastModified);
            bValue = new Date(b.LastModified);
            break;
          case 'name':
          default:
            aValue = a.Key.toLowerCase();
            bValue = b.Key.toLowerCase();
            break;
        }

        if (sortOrder === 'desc') {
          return aValue < bValue ? 1 : -1;
        }
        return aValue > bValue ? 1 : -1;
      });

      // Pagination
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedFiles = audioFiles.slice(startIndex, endIndex);

      // Format response
      const files = paginatedFiles.map(file => ({
        key: file.Key,
        name: path.basename(file.Key),
        size: file.Size,
        lastModified: file.LastModified,
        extension: path.extname(file.Key).toLowerCase().substring(1)
      }));

      return {
        files,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(audioFiles.length / limit),
          totalItems: audioFiles.length,
          itemsPerPage: limit,
          hasNextPage: endIndex < audioFiles.length,
          hasPrevPage: page > 1
        }
      };
    } catch (error) {
      console.error('Error listing files:', error);
      throw error;
    }
  }

  /**
   * Generate signed URL for file download
   */
  async generateDownloadUrl(fileKey) {
    try {
      const params = {
        Bucket: S3_CONFIG.bucketName,
        Key: fileKey,
        Expires: S3_CONFIG.signedUrlExpires,
        ResponseContentDisposition: `attachment; filename="${path.basename(fileKey)}"`
      };

      const url = await s3.getSignedUrlPromise('getObject', params);
      return url;
    } catch (error) {
      console.error('Error generating download URL:', error);
      throw error;
    }
  }

  /**
   * Generate signed URL for audio streaming
   */
  async generateStreamUrl(fileKey) {
    try {
      const params = {
        Bucket: S3_CONFIG.bucketName,
        Key: fileKey,
        Expires: S3_CONFIG.signedUrlExpires
      };

      const url = await s3.getSignedUrlPromise('getObject', params);
      return url;
    } catch (error) {
      console.error('Error generating stream URL:', error);
      throw error;
    }
  }

  /**
   * Generate signed URLs for multiple files
   */
  async generateBulkDownloadUrls(fileKeys) {
    try {
      const downloadPromises = fileKeys.map(async (key) => {
        try {
          const url = await this.generateDownloadUrl(key);
          return { key, url, success: true };
        } catch (error) {
          return { key, error: error.message, success: false };
        }
      });

      const results = await Promise.all(downloadPromises);
      return results;
    } catch (error) {
      console.error('Error generating bulk download URLs:', error);
      throw error;
    }
  }

  /**
   * Check if file exists in S3
   */
  async fileExists(fileKey) {
    try {
      await s3.headObject({
        Bucket: S3_CONFIG.bucketName,
        Key: fileKey
      }).promise();
      return true;
    } catch (error) {
      if (error.code === 'NotFound') {
        return false;
      }
      throw error;
    }
  }
}

module.exports = new FileService();
