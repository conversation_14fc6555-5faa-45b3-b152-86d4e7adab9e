const { testS3Connection, S3_CONFIG } = require('../config/aws');

/**
 * Middleware to verify S3 authentication and configuration
 */
const verifyS3Auth = async (req, res, next) => {
  try {
    // Check if S3 is properly configured
    const connectionTest = await testS3Connection();
    
    if (!connectionTest.success) {
      return res.status(503).json({
        success: false,
        message: 'S3 service unavailable',
        error: connectionTest.message
      });
    }

    // Add S3 config to request for use in routes
    req.s3Config = S3_CONFIG;
    next();
  } catch (error) {
    console.error('S3 Auth middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'S3 authentication failed',
      error: error.message
    });
  }
};

/**
 * Middleware to validate file upload parameters
 */
const validateFileUpload = (req, res, next) => {
  const { originalname, mimetype, size } = req.file || {};
  
  if (!req.file) {
    return res.status(400).json({
      success: false,
      message: 'No file provided'
    });
  }

  // Check file size
  if (size > S3_CONFIG.maxFileSize) {
    return res.status(400).json({
      success: false,
      message: `File too large. Maximum size is ${S3_CONFIG.maxFileSize / (1024 * 1024)}MB`
    });
  }

  // Check file format
  const fileExtension = originalname.split('.').pop().toLowerCase();
  if (!S3_CONFIG.allowedAudioFormats.includes(fileExtension)) {
    return res.status(400).json({
      success: false,
      message: `Invalid file format. Allowed formats: ${S3_CONFIG.allowedAudioFormats.join(', ')}`
    });
  }

  // Check MIME type
  const allowedMimeTypes = [
    'audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg', 
    'audio/mp4', 'audio/aac', 'audio/flac', 'audio/x-ms-wma'
  ];
  
  if (!allowedMimeTypes.includes(mimetype)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid MIME type for audio file'
    });
  }

  next();
};

/**
 * Generate S3 key for file storage
 */
const generateS3Key = (userId, filename) => {
  const timestamp = Date.now();
  const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_');
  return `${S3_CONFIG.uploadPath}${userId}/${timestamp}_${sanitizedFilename}`;
};

module.exports = {
  verifyS3Auth,
  validateFileUpload,
  generateS3Key
};
