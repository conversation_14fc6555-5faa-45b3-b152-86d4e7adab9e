/**
 * Global error handling middleware
 */
const errorHandler = (err, req, res, next) => {
  console.error('Error:', err);

  // Default error
  let error = {
    message: err.message || 'Internal Server Error',
    status: err.status || 500
  };

  // AWS S3 errors
  if (err.code) {
    switch (err.code) {
      case 'NoSuchBucket':
        error = {
          message: 'S3 bucket not found',
          status: 404
        };
        break;
      case 'AccessDenied':
        error = {
          message: 'Access denied to S3 resource',
          status: 403
        };
        break;
      case 'InvalidAccessKeyId':
        error = {
          message: 'Invalid AWS credentials',
          status: 401
        };
        break;
      case 'SignatureDoesNotMatch':
        error = {
          message: 'AWS signature mismatch',
          status: 401
        };
        break;
    }
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    error = {
      message: 'Invalid token',
      status: 401
    };
  }

  if (err.name === 'TokenExpiredError') {
    error = {
      message: 'Token expired',
      status: 401
    };
  }

  // Validation errors
  if (err.name === 'ValidationError') {
    error = {
      message: 'Validation failed',
      status: 400,
      details: err.details
    };
  }

  // Send error response
  res.status(error.status).json({
    success: false,
    message: error.message,
    ...(error.details && { details: error.details }),
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
};

module.exports = errorHandler;
