const express = require('express');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

/**
 * GET /api/users
 * Get all users (admin only)
 */
router.get('/', authenticateToken, requireAdmin, (req, res) => {
  // Mock implementation - replace with database query
  const users = [
    {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin',
      createdAt: '2024-01-01T00:00:00Z'
    }
  ];

  res.json({
    success: true,
    users: users.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt
    }))
  });
});

/**
 * GET /api/users/:id
 * Get user by ID
 */
router.get('/:id', authenticateToken, (req, res) => {
  const { id } = req.params;
  
  // Users can only access their own data unless they're admin
  if (req.user.role !== 'admin' && req.user.id !== parseInt(id)) {
    return res.status(403).json({
      success: false,
      message: 'Access denied'
    });
  }

  // Mock implementation
  const user = {
    id: parseInt(id),
    username: 'admin',
    email: '<EMAIL>',
    role: 'admin',
    createdAt: '2024-01-01T00:00:00Z'
  };

  res.json({
    success: true,
    user: {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt
    }
  });
});

module.exports = router;
