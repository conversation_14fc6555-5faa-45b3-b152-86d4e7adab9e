const express = require('express');
const { query, param, validationResult } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const { verifyS3Auth, validateFileUpload } = require('../middleware/s3Auth');
const fileService = require('../services/fileService');
const multer = require('multer');

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB
  }
});

const router = express.Router();

/**
 * GET /api/files
 * List audio files from S3 bucket
 */
router.get('/', authenticateToken, verifyS3Auth, [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('search').optional().isString().withMessage('Search must be a string'),
  query('sortBy').optional().isIn(['name', 'size', 'lastModified']).withMessage('Invalid sort field'),
  query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('Sort order must be asc or desc')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      search = '',
      sortBy = 'name',
      sortOrder = 'asc'
    } = req.query;

    const result = await fileService.listFiles({
      page: parseInt(page),
      limit: parseInt(limit),
      search,
      sortBy,
      sortOrder
    });

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/files/:key/download
 * Generate signed URL for file download
 */
router.get('/:key/download', authenticateToken, [
  param('key').notEmpty().withMessage('File key is required')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { key } = req.params;
    const downloadUrl = await fileService.generateDownloadUrl(key);

    res.json({
      success: true,
      downloadUrl
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/files/:key/stream
 * Generate signed URL for audio streaming
 */
router.get('/:key/stream', authenticateToken, [
  param('key').notEmpty().withMessage('File key is required')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { key } = req.params;
    const streamUrl = await fileService.generateStreamUrl(key);

    res.json({
      success: true,
      streamUrl
    });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/files/bulk-download
 * Generate signed URLs for multiple files
 */
router.post('/bulk-download', authenticateToken, async (req, res, next) => {
  try {
    const { fileKeys } = req.body;

    if (!Array.isArray(fileKeys) || fileKeys.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'File keys array is required'
      });
    }

    const downloadUrls = await fileService.generateBulkDownloadUrls(fileKeys);

    res.json({
      success: true,
      downloadUrls
    });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/files/upload
 * Upload audio file to S3
 */
router.post('/upload', authenticateToken, verifyS3Auth, upload.single('audioFile'), validateFileUpload, async (req, res, next) => {
  try {
    const { file, user } = req;
    const metadata = {
      title: req.body.title || file.originalname,
      description: req.body.description || '',
      tags: req.body.tags || ''
    };

    const uploadResult = await fileService.uploadFile(file, user.id, metadata);

    res.status(201).json({
      success: true,
      message: 'File uploaded successfully',
      file: uploadResult
    });
  } catch (error) {
    console.error('File upload error:', error);
    next(error);
  }
});

/**
 * GET /api/files/s3-status
 * Check S3 connection status
 */
router.get('/s3-status', authenticateToken, async (req, res, next) => {
  try {
    const { testS3Connection } = require('../config/aws');
    const status = await testS3Connection();

    res.json({
      success: true,
      s3Status: status
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
