# Audio File Management - Backend

Node.js/Express API server for the Audio File Management application.

## Features

- JWT-based authentication
- AWS S3 integration for file storage
- RESTful API endpoints
- File streaming and download
- Search and filtering
- Bulk operations
- Security middleware
- Error handling
- Health checks

## Quick Start

1. **Install dependencies:**
```bash
npm install
```

2. **Configure environment:**
```bash
cp ../.env.example ../.env
# Edit .env with your configuration
```

3. **Start development server:**
```bash
npm run dev
```

4. **Run tests:**
```bash
npm test
```

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### Files
- `GET /api/files` - List audio files
- `GET /api/files/:key/download` - Generate download URL
- `GET /api/files/:key/stream` - Generate streaming URL
- `POST /api/files/bulk-download` - Bulk download URLs

### Users
- `GET /api/users` - List users (admin only)
- `GET /api/users/:id` - Get user by ID

### Health
- `GET /api/health` - Health check

## Environment Variables

Required environment variables:

```bash
NODE_ENV=development
PORT=3000
JWT_SECRET=your-jwt-secret
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-bucket-name
CORS_ORIGIN=http://localhost:5173
```

## Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues

## Project Structure

```
src/
├── config/
│   └── aws.js              # AWS S3 configuration
├── middleware/
│   ├── auth.js             # Authentication middleware
│   └── errorHandler.js     # Global error handler
├── routes/
│   ├── auth.js             # Authentication routes
│   ├── files.js            # File management routes
│   └── users.js            # User management routes
├── services/
│   └── fileService.js      # S3 file operations
└── server.js               # Main application file
```

## Authentication

The API uses JWT tokens for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

Default credentials:
- Username: `admin`
- Password: `password`

## AWS S3 Setup

1. Create an S3 bucket
2. Configure bucket permissions
3. Set up IAM user with S3 access
4. Add credentials to environment variables

Required S3 permissions:
- `s3:GetObject`
- `s3:ListBucket`

## Error Handling

The API returns consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "details": "Additional details (optional)"
}
```

## Security Features

- Helmet.js for security headers
- CORS configuration
- Rate limiting
- Input validation
- JWT token expiration
- Error message sanitization

## Development

### Adding New Routes

1. Create route file in `src/routes/`
2. Register route in `src/server.js`
3. Add authentication middleware if needed
4. Implement input validation
5. Add error handling

### Testing

Tests are written using Jest and Supertest:

```javascript
const request = require('supertest');
const app = require('../src/server');

describe('GET /api/health', () => {
  it('should return health status', async () => {
    const res = await request(app)
      .get('/api/health')
      .expect(200);
    
    expect(res.body.status).toBe('OK');
  });
});
```

## Deployment

### Docker

```bash
docker build -t audio-backend .
docker run -p 3000:3000 --env-file .env audio-backend
```

### PM2

```bash
npm install -g pm2
pm2 start src/server.js --name audio-backend
```

## Monitoring

Health check endpoint: `GET /api/health`

Response:
```json
{
  "status": "OK",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "environment": "development"
}
```

## Troubleshooting

### Common Issues

1. **S3 Access Denied**: Check AWS credentials and bucket permissions
2. **CORS Errors**: Verify CORS_ORIGIN environment variable
3. **JWT Errors**: Ensure JWT_SECRET is set and consistent
4. **Port Already in Use**: Change PORT environment variable

### Logs

Development logs are output to console. In production, consider using a logging service like Winston or Morgan.
