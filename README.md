# Audio File Management Web Application

A web-based application designed to streamline the browsing and management of audio files stored in Amazon S3 buckets.

## Project Structure

```
Audio_File_Management/
├── backend/                    # Node.js backend
├── frontend/                   # Vue 3 frontend
├── docs/                       # Documentation
├── docker/                     # Docker configuration
├── scripts/                    # Build and deployment scripts
├── .env.example               # Environment variables template
├── .gitignore                 # Git ignore rules
├── docker-compose.yml         # Docker compose configuration
└── README.md                  # This file
```

## Features

- User authentication and secure S3 connection
- Audio file browsing with metadata display
- In-browser audio playbook
- Search and filter functionality
- Individual and bulk file downloads
- Responsive design for desktop and mobile

## Tech Stack

- **Backend**: Node.js, Express.js
- **Frontend**: Vue 3, Vite
- **Storage**: Amazon S3
- **Authentication**: JWT tokens
- **Database**: (TBD - for user management and metadata caching)

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- AWS S3 bucket access credentials

### Installation

1. Clone the repository
2. Install backend dependencies: `cd backend && npm install`
3. Install frontend dependencies: `cd frontend && npm install`
4. Configure environment variables (see .env.example)
5. Start development servers

## Development

- Backend runs on port 3000
- Frontend runs on port 5173 (Vite default)

## Functional Requirements

- FR1: Secure user login
- FR2: S3 bucket connection
- FR3: Audio file listing with metadata
- FR4: In-browser audio playback
- FR5: File search functionality
- FR6: Metadata filtering
- FR7: File download capabilities
- FR8: Responsive design
- FR9: Access control and security
