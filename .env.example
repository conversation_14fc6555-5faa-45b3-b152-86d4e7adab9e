# Backend Configuration
NODE_ENV=development
PORT=3000
API_BASE_URL=http://localhost:3000

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-audio-files-bucket

# Database Configuration (if using database for user management)
# DATABASE_URL=postgresql://username:password@localhost:5432/audio_management
# MONGODB_URI=mongodb://localhost:27017/audio_management

# CORS Configuration
CORS_ORIGIN=http://localhost:5173

# File Upload Configuration
MAX_FILE_SIZE=100MB
ALLOWED_AUDIO_FORMATS=mp3,wav,ogg,m4a,aac

# Frontend Configuration (for reference)
# VITE_API_BASE_URL=http://localhost:3000
# VITE_APP_NAME=Audio File Management
