# 🎧 Audio File Management Web Application

A secure, responsive web-based application for browsing, previewing, and downloading audio files from Amazon S3. Designed for implementation across all subsidiaries.

---

## 📅 Project Timeline

**Project Duration:** July 24, 2025 – August 16, 2025

| Week | Date Range             | Focus Area                          |
|------|------------------------|--------------------------------------|
| 1    | July 24 – July 27      | Project Setup & UI Design            |
| 2    | July 28 – August 3     | Frontend Development                 |
| 3    | August 4 – August 10   | Backend Development & Integration    |
| 4    | August 11 – August 16  | Testing, QA, and Deployment          |

---

## ✅ Task Breakdown

### 📁 Phase 1: Planning & UI Design (July 24–27)

- [ ] Finalize functional requirements
- [ ] Prepare Figma UI wireframes
- [ ] Define API endpoints (Node.js + S3)
- [ ] Confirm access to Amazon S3 credentials

---

### 🎨 Phase 2: Frontend Development (Vue 3) (July 28 – August 3)

- [ ] Setup Vue 3 + Tailwind project
- [ ] Create Login Page (FR1)
- [ ] Create Audio List Page with metadata (FR3)
- [ ] Implement Search + Filter bar (FR5, FR6)
- [ ] Add In-browser audio preview using `<audio>` tag (FR4)
- [ ] Add multi-select download UI + file selectors (FR7)
- [ ] Ensure responsive UI across devices (FR8)

---

### 🧠 Phase 3: Backend Development (Node.js) (August 4 – August 8)

- [ ] Setup Express server + middleware
- [ ] Implement secure login + session/token auth (FR1, FR9)
- [ ] Integrate AWS SDK to connect to S3 bucket (FR2)
- [ ] Fetch & send audio file metadata to frontend (FR3)
- [ ] Generate pre-signed S3 download URLs (FR7)
- [ ] Handle CORS, logging, and basic error handling

---

### 🔗 Phase 4: Integration & Final Touches (August 9 – August 11)

- [ ] Connect frontend to backend API
- [ ] Validate search, filter, and playback features
- [ ] Validate download (single & bulk)
- [ ] Add logout functionality

---

### 🧪 Phase 5: Testing & UAT (August 12 – August 14)

- [ ] Cross-browser and mobile testing
- [ ] Security tests (token expiry, access control)
- [ ] UAT feedback session with stakeholders

---

### 🚀 Phase 6: Deployment & Handover (August 15 – August 16)

- [ ] Setup production build for frontend
- [ ] Deploy backend to server/cloud
- [ ] Share deployment credentials and S3 configurations
- [ ] Final README and documentation handover

---

## 🔐 Functional Requirements Summary

| ID  | Description                                                                 |
|-----|-----------------------------------------------------------------------------|
| FR1 | Secure user login                                                           |
| FR2 | Connect to Amazon S3 with credentials                                       |
| FR3 | List audio files with metadata (name, size, date)                           |
| FR4 | Play audio directly in the browser                                          |
| FR5 | Search audio files by name                                                  |
| FR6 | Filter files by size/date                                                   |
| FR7 | Select and download single/multiple files                                   |
| FR8 | Responsive layout for desktop and mobile                                    |
| FR9 | Prevent unauthorized access via authentication & access control             |

---

## 🔧 Tech Stack

- **Frontend**: Vue 3, TailwindCSS
- **Backend**: Node.js, Express
- **Cloud Storage**: Amazon S3
- **Auth**: Token/session-based login
- **Media**: HTML5 `<audio>` tag

---

## 🗂 Folder Structure (Planned)

